import cv2
import pytesseract
import numpy as np

# Set the path to the Tesseract executable if it's not in your PATH
# Replace with your actual path to tesseract.exe if needed
# For example: r'C:\Program Files\Tesseract-OCR\tesseract.exe'
# If Tesseract is in PATH, this line can be commented out or an empty string.
TESSERACT_CMD = r'H:\Tesseract OCR\tesseract.exe'

if TESSERACT_CMD:
    pytesseract.pytesseract.tesseract_cmd = TESSERACT_CMD

def preprocess_image_for_ocr(image: np.ndarray) -> np.ndarray:
    """
    Preprocesses the image for better OCR results.
    This can include grayscaling, thresholding, resizing, etc.
    """
    # Convert to grayscale
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # Apply thresholding to get a binary image
    # Adjust thresholding values as needed based on game UI
    # For light text on dark background or vice-versa, adaptive thresholding might be better
    _ , binary_image = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)
    
    # TODO: Consider other preprocessing steps:
    # - Denoising (e.g., cv2.medianBlur)
    # - Resizing/Scaling (Tesseract works well with DPI around 300)
    # - Sharpening
    
    return binary_image

def extract_text_from_image(image: np.ndarray, lang: str = 'chi_sim') -> str:
    """
    Extracts text from a given image using Tesseract OCR.

    Args:
        image: The image (NumPy array) to extract text from.
        lang: The language code for Tesseract (e.g., 'eng', 'chi_sim').

    Returns:
        The extracted text as a string.
    """
    try:
        # Preprocess the image for potentially better OCR results
        # processed_image = preprocess_image_for_ocr(image)
        # For now, let's try with the raw image first, or simple grayscale
        gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Perform OCR
        # --psm (Page Segmentation Mode) can be tuned. Default is 3.
        #    6 assumes a single uniform block of text.
        #    7 treats the image as a single text line.
        #    11 sparse text. Find as much text as possible in no particular order.
        custom_config = r'--oem 3 --psm 6' # Example config
        text = pytesseract.image_to_string(gray_image, lang=lang, config=custom_config)
        return text.strip()
    except pytesseract.TesseractNotFoundError:
        print("ERROR: Tesseract is not installed or not found in your PATH.")
        print(f"Attempted to use tesseract_cmd: {pytesseract.pytesseract.tesseract_cmd}")
        print("Please ensure Tesseract is installed and its path is correctly set.")
        return ""
    except Exception as e:
        print(f"An error occurred during OCR: {e}")
        return ""

if __name__ == '__main__':
    # This is example code for testing image_analyzer.py directly.
    # You would need an actual image file to test this.
    # For example, save a screenshot of your game's quest log as 'test_quest_log.png'
    # in the same directory as this script.

    print(f"Using Tesseract Command: {pytesseract.pytesseract.tesseract_cmd}")
    
    # Create a dummy black image with some white text for basic testing
    # This is just to ensure pytesseract is callable, actual game OCR will be more complex.
    dummy_image = np.zeros((100, 400, 3), dtype=np.uint8) # Black background BGR
    cv2.putText(dummy_image, "Hello World", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2, cv2.LINE_AA)
    cv2.putText(dummy_image, "你好世界", (50, 80), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2, cv2.LINE_AA) # Chinese
    
    print("Testing English OCR on dummy image...")
    eng_text = extract_text_from_image(dummy_image, lang='eng')
    print(f"Extracted English Text: '{eng_text}'")

    print("\nTesting Chinese OCR on dummy image...")
    # If chi_sim is not installed with Tesseract, this will likely fail or return garbage.
    chi_text = extract_text_from_image(dummy_image, lang='chi_sim')
    print(f"Extracted Chinese Text: '{chi_text}'")

    # To test with an actual game image:
    # try:
    #     game_image = cv2.imread('test_game_screen.png') # Load a test image
    #     if game_image is None:
    #         print("Error: Could not load test_game_screen.png. Make sure it exists.")
    #     else:
    #         print("\nTesting OCR on 'test_game_screen.png'...")
    #         extracted_game_text = extract_text_from_image(game_image, lang='chi_sim')
    #         print(f"Extracted Game Text: '{extracted_game_text}'")
    # except Exception as e:
    #     print(f"Error during game image test: {e}")

    print("\nIf Chinese text is not recognized correctly, ensure:")
    print("1. Tesseract OCR is installed correctly.")
    print("2. The 'chi_sim' language data (chi_sim.traineddata) is in the 'tessdata' directory.")
    print(f"   (Expected tessdata path for H:\\Tesseract OCR is H:\\Tesseract OCR\\tessdata)")
    print("3. The TESSERACT_CMD path at the top of this script is correct if Tesseract is not in PATH.")
    print("4. The image quality and preprocessing are suitable for OCR.") 