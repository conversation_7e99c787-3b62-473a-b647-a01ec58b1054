import pygetwindow as gw
import pyautogui
import numpy as np
from PIL import ImageGrab, Image
from typing import Union

def capture_game_window(window_title: str) -> Union[np.ndarray, None]:
    """
    Captures the specified game window.

    Args:
        window_title: The title of the game window.

    Returns:
        A NumPy array representing the captured image in BGR format if successful, 
        None otherwise.
    """
    try:
        game_windows = gw.getWindowsWithTitle(window_title)
        if not game_windows:
            print(f"Error: Window with title '{window_title}' not found.")
            return None
        
        game_window = game_windows[0] # Assume the first found window is the correct one

        if not game_window.isActive:
            try:
                game_window.activate() # Try to activate the window
                pyautogui.sleep(0.2) # Give it a moment to activate
            except Exception as e:
                print(f"Warning: Could not activate window: {e}")


        # Get window geometry
        x, y, width, height = game_window.left, game_window.top, game_window.width, game_window.height

        if width <= 0 or height <= 0:
            print(f"Error: Window '{window_title}' has invalid dimensions (width: {width}, height: {height}). Is it minimized?")
            return None

        # Capture the window content
        screenshot = ImageGrab.grab(bbox=(x, y, x + width, y + height), all_screens=True)
        
        # Convert to NumPy array
        img_np = np.array(screenshot)
        
        # Convert RGB to BGR (OpenCV format)
        img_bgr = img_np[:, :, ::-1].copy() # Make a writable copy
        
        return img_bgr

    except Exception as e:
        print(f"An error occurred during screen capture: {e}")
        # Attempt to capture full screen as a fallback if specific window fails
        try:
            print("Attempting to capture full screen as a fallback...")
            screenshot_fs = pyautogui.screenshot()
            img_np_fs = np.array(screenshot_fs)
            img_bgr_fs = img_np_fs[:, :, ::-1].copy()
            return img_bgr_fs
        except Exception as e_fs:
            print(f"Fallback full screen capture also failed: {e_fs}")
            return None

if __name__ == '__main__':
    # Example usage: Replace with your actual game window title
    game_title = "剑网3无界 - 龙争虎斗 @ 电信区(点月卡区)" # Example, use your actual title
    
    # Try to find any window if specific title fails, for testing
    if not gw.getWindowsWithTitle(game_title):
        print(f"Specific window '{game_title}' not found. Trying to find any active window for testing.")
        all_windows = gw.getAllWindows()
        visible_windows = [w for w in all_windows if w.visible and w.title]
        if visible_windows:
            game_title = visible_windows[0].title
            print(f"Using '{game_title}' for testing.")
        else:
            print("No visible windows found for testing. Please ensure a window is open.")
            exit()
            
    captured_image = capture_game_window(game_title)

    if captured_image is not None:
        # We need opencv to show the image, which might not be installed yet.
        # For now, just print dimensions as a test.
        print(f"Captured image with shape: {captured_image.shape}")
        # To display, you would use:
        # import cv2
        # cv2.imshow("Game Capture", captured_image)
        # cv2.waitKey(0)
        # cv2.destroyAllWindows()
    else:
        print("Failed to capture image.") 