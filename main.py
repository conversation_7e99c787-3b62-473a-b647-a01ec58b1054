import cv2
import time
from utils.screen_capture import capture_game_window
登录
# The title of the game window
# You might need to adjust this if it's not exact or if the window title changes.
GAME_WINDOW_TITLE = "剑网3无界 - 龙争虎斗 @ 电信区(点月卡区)"


def main():
    print("Starting game automation script...")
    print(f"Attempting to capture window: {GAME_WINDOW_TITLE}")

    while True:
        # Capture the game window
        frame = capture_game_window(GAME_WINDOW_TITLE)

        if frame is not None:
            # --- Image processing and decision making will go here ---
            # For now, we'll just display the captured frame
            cv2.imshow("Game View", frame)
            
            # Placeholder for future logic
            # analyze_frame(frame)
            # execute_actions(based_on_analysis)

            print(f"Frame captured, shape: {frame.shape}") # Log frame capture

        else:
            print("Failed to capture frame. Is the game window open and not minimized?")
            # Wait a bit before retrying if the window is not found
            time.sleep(2)

        # Press 'q' to quit the main loop (and close the display window)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            print("Exiting script...")
            break
        
        # Control loop speed (e.g., 10 FPS)
        # time.sleep(0.1) 

    cv2.destroyAllWindows()
    print("Script finished.")

if __name__ == "__main__":
    main() 